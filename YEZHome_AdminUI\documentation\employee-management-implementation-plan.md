# Kế hoạch triển khai màn hình quản lý nhân viên

## Tổng quan
Màn hình quản lý nhân viên sẽ cho phép quản trị viên xem, thê<PERSON>, sửa và xóa thông tin nhân viên trong hệ thống. Màn hình này sẽ được phát triển dựa trên màn hình quản lý bất động sản hiện có.

## Các tính năng chính
1. **Khu vực bộ lọc**: L<PERSON><PERSON> nhân viên theo email, tên và số điện thoại
2. **Bảng dữ liệu**: Hiển thị danh sách nhân viên với phân trang và sắp xếp
3. **Thêm nhân viên**: Nút thêm nhân viên mới thông qua modal popup
4. **Quản lý nhân viên**: <PERSON><PERSON><PERSON> tù<PERSON> chọn chỉnh sửa, xem chi tiết và xóa cho mỗi nhân viên

## Cấu trúc API
Các API endpoint sẽ được sử dụng:
- `GET /api/User/employees`: Lấy danh sách nhân viên với bộ lọc và phân trang
- `POST /api/User/employees`: Thêm nhân viên mới
- `GET /api/User/{id}`: Lấy thông tin chi tiết của nhân viên
- `PUT /api/User/{userId}/status`: Cập nhật trạng thái nhân viên (kích hoạt/vô hiệu hóa)

## Giao diện người dùng

### 1. Khu vực bộ lọc
- Các trường tìm kiếm: Email, Tên, Số điện thoại
- Nút "Áp dụng bộ lọc" để gửi yêu cầu tìm kiếm
- Nút "Thêm nhân viên" để mở modal thêm nhân viên mới

### 2. Bảng dữ liệu nhân viên
- Các cột: Họ tên, Email, Số điện thoại, Loại người dùng, Trạng thái, Ngày tạo, Hành động
- Phân trang với hiển thị số trang và điều hướng
- Sắp xếp theo các cột

### 3. Modal thêm nhân viên
- Form với các trường: Họ tên, Email, Mật khẩu, Số điện thoại, Vai trò
- Nút "Thêm" để lưu thông tin
- Nút "Thoát" để đóng modal

### 4. Modal chỉnh sửa/xem chi tiết nhân viên
- Hiển thị thông tin chi tiết của nhân viên
- Chế độ xem: Chỉ hiển thị thông tin
- Chế độ chỉnh sửa: Cho phép thay đổi thông tin
- Nút "Lưu" (chỉ có ở chế độ chỉnh sửa)
- Nút "Đóng" để đóng modal

### 5. Modal xác nhận xóa
- Thông báo cảnh báo về việc xóa vĩnh viễn
- Nút "Xác nhận xóa" để thực hiện xóa
- Nút "Thoát" để hủy và đóng modal

## Luồng làm việc

### Hiển thị danh sách nhân viên
1. Tải dữ liệu nhân viên từ API với các tham số mặc định
2. Hiển thị dữ liệu trong bảng với phân trang

### Lọc danh sách nhân viên
1. Người dùng nhập các tiêu chí tìm kiếm
2. Khi nhấn "Áp dụng bộ lọc", gửi yêu cầu API với các tham số tìm kiếm
3. Cập nhật bảng dữ liệu với kết quả trả về

### Thêm nhân viên mới
1. Người dùng nhấn "Thêm nhân viên"
2. Hiển thị modal với form thêm nhân viên
3. Người dùng nhập thông tin và nhấn "Thêm"
4. Gửi yêu cầu API để tạo nhân viên mới
5. Nếu thành công, đóng modal và làm mới danh sách nhân viên
6. Nếu thất bại, hiển thị thông báo lỗi

### Chỉnh sửa/Xem chi tiết nhân viên
1. Người dùng nhấn vào tùy chọn "Chỉnh sửa" hoặc "Xem chi tiết" từ menu dropdown
2. Lấy thông tin chi tiết của nhân viên từ API
3. Hiển thị modal với thông tin nhân viên
4. Trong chế độ chỉnh sửa, người dùng có thể thay đổi thông tin và lưu
5. Sau khi lưu thành công, đóng modal và làm mới danh sách

### Xóa nhân viên
1. Người dùng nhấn vào tùy chọn "Xóa" từ menu dropdown
2. Hiển thị modal xác nhận xóa
3. Nếu người dùng xác nhận, gửi yêu cầu API để xóa nhân viên
4. Sau khi xóa thành công, làm mới danh sách nhân viên

## Kế hoạch triển khai
1. Tạo file `src/routes/_authenticated/employee/index.tsx`
2. Tạo các component cần thiết:
   - Bảng danh sách nhân viên
   - Form tìm kiếm
   - Modal thêm nhân viên
   - Modal chỉnh sửa/xem chi tiết
   - Modal xác nhận xóa
3. Tạo service để gọi API liên quan đến nhân viên
4. Tích hợp với hệ thống định tuyến và quản lý trạng thái
5. Kiểm tra và tối ưu hóa hiệu suất

## Lưu ý
- Đảm bảo xác thực và phân quyền phù hợp
- Xử lý lỗi và hiển thị thông báo phù hợp
- Đảm bảo giao diện người dùng nhất quán với phần còn lại của ứng dụng 
﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public UserService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<UserDto> GetUserByIdAsync(Guid id)
        {
            var user = await _unitOfWork.AppUsers.GetQueryable()
                .Where(u => u.Id == id)
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .ThenInclude(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync();

            if (user == null)
                return null;

            var userDto = _mapper.Map<UserDto>(user);

            // Get user's wallet
            var wallet = await _unitOfWork.Wallets.GetQueryable()
                .FirstOrDefaultAsync(w => w.UserId == id);

            if (wallet == null)
            {
                // Create a new wallet if it doesn't exist
                wallet = new Wallet
                {
                    UserId = id,
                    Balance = 0
                };

                await _unitOfWork.Wallets.AddAsync(wallet);
            }

            // Get user's notification preferences
            var preferences = (await _unitOfWork.NotificationPreferences.FindAsync(p => p.UserId == id)).FirstOrDefault();
            if (preferences == null)
            {
                // Create default notification preferences if they don't exist
                preferences = new NotificationPreference
                {
                    UserId = id,
                    ReceivePromotions = true,
                    ReceiveWalletUpdates = true,
                    ReceiveNews = true,
                    ReceiveCustomerMessages = true,
                    CreatedAt = DateTime.UtcNow
                };

                await _unitOfWork.NotificationPreferences.AddAsync(preferences);
            }

            // Save changes if we created a wallet or notification preferences
            if (wallet == null || preferences == null)
            {
                await _unitOfWork.SaveChangesAsync();
            }

            userDto.Wallet = _mapper.Map<WalletDto>(wallet);

            // Map invoice information
            userDto.PersonalTaxCode = user.PersonalTaxCode;
            userDto.InvoiceInfo = new UserInvoiceInfoDto
            {
                BuyerName = user.InvoiceBuyerName,
                Email = user.InvoiceEmail,
                CompanyName = user.InvoiceCompanyName,
                TaxCode = user.InvoiceTaxCode,
                Address = user.InvoiceAddress
            };

            // Include role and permission data for admin users
            if (user.UserType == EnumValues.UserType.Admin.ToString())
            {
                // Get role codes (existing string array)
                var roleCodes = user.UserRoles?.Select(ur => ur.Role.Code).ToList() ?? new List<string>();
                userDto.Roles = roleCodes;

                // Get permission codes (existing string array)
                var permissionCodes = user.UserRoles?
                    .SelectMany(ur => ur.Role.RolePermissions ?? new List<RolePermission>())
                    .Select(rp => rp.Permission.Code)
                    .Distinct()
                    .ToList() ?? new List<string>();
                userDto.Permissions = permissionCodes;

                // Get role objects (new detailed objects)
                var roles = user.UserRoles?.Select(ur => ur.Role).ToList() ?? new List<AdminRole>();
                userDto.RoleObjects = _mapper.Map<IEnumerable<AdminRoleDto>>(roles);

                // Get permission objects (new detailed objects)
                var permissions = user.UserRoles?
                    .SelectMany(ur => ur.Role.RolePermissions ?? new List<RolePermission>())
                    .Select(rp => rp.Permission)
                    .Distinct()
                    .ToList() ?? new List<Permission>();
                userDto.PermissionObjects = _mapper.Map<IEnumerable<PermissionDto>>(permissions);
            }

            return userDto;
        }

        public async Task<UserDto> GetUserByEmailAsync(string email)
        {
            var user = await _unitOfWork.AppUsers.GetByEmailAsync(email);
            return _mapper.Map<UserDto>(user);
        }

        public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
        {
            var users = await _unitOfWork.AppUsers.GetAllAsync();
            return _mapper.Map<IEnumerable<UserDto>>(users);
        }

        public async Task<UserDto> CreateUserAsync(CreateUserDto userDto)
        {
            var user = _mapper.Map<AppUser>(userDto);
            await _unitOfWork.AppUsers.AddAsync(user);

            // Create default notification preferences for the user
            var notificationPreferences = new NotificationPreference
            {
                UserId = user.Id,
                ReceivePromotions = true,
                ReceiveWalletUpdates = true,
                ReceiveNews = true,
                ReceiveCustomerMessages = true,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.NotificationPreferences.AddAsync(notificationPreferences);

            // Create default wallet for the user
            var wallet = new Wallet
            {
                UserId = user.Id,
                Balance = 0
            };

            await _unitOfWork.Wallets.AddAsync(wallet);

            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<UserDto>(user);
        }

        public async Task<bool> UpdateUserAsync(Guid id, CreateUserDto userDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(id);
            if (user == null) return false;
            _mapper.Map(userDto, user);
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteUserAsync(Guid id)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(id);
            if (user == null) return false;
            _unitOfWork.AppUsers.Remove(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AddUserRoleAsync(AddUserRoleDto addUserRoleDto)
        {
            var userRole = _mapper.Map<UserRole>(addUserRoleDto);
            await _unitOfWork.UserRoles.AddAsync(userRole);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateUserAvatarAsync(Guid userId, string avatarImage)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            user.AvatarImage = avatarImage;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<string> GetUserAvatarImageAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            return user?.AvatarImage;
        }

        public async Task<bool> DeactivateUserAsync(Guid userId, DeactivateUserDto deactivateUserDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            // Validate password before deactivating
            var authService = new AuthService(_unitOfWork, null, _mapper);
            var isValidCredentials = await authService.ValidateUserCredentialsAsync(user.Email, deactivateUserDto.Password);
            if (!isValidCredentials) return false;

            // Deactivate the user
            user.IsActive = false;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ReactivateUserAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            // Reactivate the user
            user.IsActive = true;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> PermanentDeleteUserAsync(Guid userId, DeactivateUserDto deactivateUserDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            // Validate password before permanent deletion
            var authService = new AuthService(_unitOfWork, null, _mapper);
            var isValidCredentials = await authService.ValidateUserCredentialsAsync(user.Email, deactivateUserDto.Password);
            if (!isValidCredentials) return false;

            // Delete related data
            // 1. Delete user roles
            var userRoles = await _unitOfWork.UserRoles.GetQueryable()
                .Where(ur => ur.UserID == userId)
                .ToListAsync();
            foreach (var role in userRoles)
            {
                _unitOfWork.UserRoles.Remove(role);
            }

            // 2. Delete wallet and transactions
            var wallet = await _unitOfWork.Wallets.GetQueryable()
                .FirstOrDefaultAsync(w => w.UserId == userId);
            if (wallet != null)
            {
                var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                    .Where(t => t.UserId == userId)
                    .ToListAsync();
                foreach (var transaction in transactions)
                {
                    _unitOfWork.WalletTransactions.Remove(transaction);
                }
                _unitOfWork.Wallets.Remove(wallet);
            }

            // 3. Delete user favorites
            var favorites = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => f.UserID == userId)
                .ToListAsync();
            foreach (var favorite in favorites)
            {
                _unitOfWork.UserFavorites.Remove(favorite);
            }


            // 8. Delete properties
            var properties = await _unitOfWork.Properties.GetQueryable()
                .Where(p => p.OwnerID == userId)
                .ToListAsync();
            foreach (var property in properties)
            {
                // Delete property media
                var propertyMedia = await _unitOfWork.PropertyMedias.GetQueryable()
                    .Where(pm => pm.PropertyID == property.Id)
                    .ToListAsync();
                foreach (var media in propertyMedia)
                {
                    _unitOfWork.PropertyMedias.Remove(media);
                }

                // Delete property status logs
                var statusLogs = await _unitOfWork.PropertyStatusLogs.GetQueryable()
                    .Where(sl => sl.PropertyID == property.Id)
                    .ToListAsync();
                foreach (var log in statusLogs)
                {
                    _unitOfWork.PropertyStatusLogs.Remove(log);
                }

                _unitOfWork.Properties.Remove(property);
            }

            // Finally, delete the user
            _unitOfWork.AppUsers.Remove(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateUserTaxInfoAsync(Guid userId, UpdateUserTaxInfoDto taxInfoDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            // Update personal tax code
            user.PersonalTaxCode = taxInfoDto.PersonalTaxCode;

            // Update invoice information if provided
            if (taxInfoDto.InvoiceInfo != null)
            {
                user.InvoiceBuyerName = taxInfoDto.InvoiceInfo.BuyerName;
                user.InvoiceEmail = taxInfoDto.InvoiceInfo.Email;
                user.InvoiceCompanyName = taxInfoDto.InvoiceInfo.CompanyName;
                user.InvoiceTaxCode = taxInfoDto.InvoiceInfo.TaxCode;
                user.InvoiceAddress = taxInfoDto.InvoiceInfo.Address;
            }

            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<UserInvoiceInfoDto> GetUserInvoiceInfoAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return null;

            return new UserInvoiceInfoDto
            {
                BuyerName = user.InvoiceBuyerName,
                Email = user.InvoiceEmail,
                CompanyName = user.InvoiceCompanyName,
                TaxCode = user.InvoiceTaxCode,
                Address = user.InvoiceAddress
            };
        }

        public async Task<bool> IsUserExistsAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            return user != null;
        }

        // Permission-related methods
        public async Task<IEnumerable<PermissionDto>> GetUserPermissionsAsync(Guid userId)
        {
            var userRoles = await _unitOfWork.UserRoles.GetQueryable()
                .Where(ur => ur.UserID == userId)
                .Include(ur => ur.Role)
                .ThenInclude(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .ToListAsync();

            var permissions = userRoles
                .SelectMany(ur => ur.Role.RolePermissions ?? new List<RolePermission>())
                .Select(rp => rp.Permission)
                .Distinct()
                .ToList();

            return _mapper.Map<IEnumerable<PermissionDto>>(permissions);
        }

        public async Task<bool> UserHasPermissionAsync(Guid userId, string permissionCode)
        {
            var userPermissions = await GetUserPermissionsAsync(userId);
            return userPermissions.Any(p => p.Code == permissionCode);
        }

        public async Task<IEnumerable<AdminRoleDto>> GetUserRolesAsync(Guid userId)
        {
            var userRoles = await _unitOfWork.UserRoles.GetQueryable()
                .Where(ur => ur.UserID == userId)
                .Include(ur => ur.Role)
                .ToListAsync();

            var roles = userRoles.Select(ur => ur.Role).ToList();
            return _mapper.Map<IEnumerable<AdminRoleDto>>(roles);
        }

        public async Task<bool> IsUserAdminExistsAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.FindAsync(x => x.Id == userId && x.UserType == EnumValues.UserType.Admin.ToString());
            return user != null;
        }

        #region User Management Methods for Admin

        public async Task<PagedResultDto<UserDto>> GetNonAdminUsersAsync(UserFilterDto filter)
        {
            var query = _unitOfWork.AppUsers.GetQueryable()
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .Where(u => u.UserType != EnumValues.UserType.Admin.ToString() && !u.IsDeleted);

            // Apply filters
            query = ApplyUserFilters(query, filter);

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply sorting
            query = ApplyUserSorting(query, filter);

            // Apply pagination
            var users = await query
                .Skip((filter.PageNumber - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            // Map to DTOs
            var userDtos = new List<UserDto>();
            foreach (var user in users)
            {
                var userDto = _mapper.Map<UserDto>(user);

                // Get wallet information
                var wallet = await _unitOfWork.Wallets.GetQueryable()
                    .FirstOrDefaultAsync(w => w.UserId == user.Id);
                userDto.Wallet = wallet != null ? _mapper.Map<WalletDto>(wallet) : null;

                // Map invoice information
                userDto.PersonalTaxCode = user.PersonalTaxCode;
                userDto.InvoiceInfo = new UserInvoiceInfoDto
                {
                    BuyerName = user.InvoiceBuyerName,
                    Email = user.InvoiceEmail,
                    CompanyName = user.InvoiceCompanyName,
                    TaxCode = user.InvoiceTaxCode,
                    Address = user.InvoiceAddress
                };

                userDtos.Add(userDto);
            }

            var pageCount = (int)Math.Ceiling(totalCount / (double)filter.PageSize);

            return new PagedResultDto<UserDto>
            {
                Items = userDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = filter.PageNumber,
                PageSize = filter.PageSize
            };
        }

        public async Task<PagedResultDto<UserDto>> GetAdminUsersAsync(UserFilterDto filter)
        {
            var query = _unitOfWork.AppUsers.GetQueryable()
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .ThenInclude(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .Where(u => u.UserType == EnumValues.UserType.Admin.ToString() && !u.IsDeleted);

            // Apply filters
            query = ApplyUserFilters(query, filter);

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply sorting
            query = ApplyUserSorting(query, filter);

            // Apply pagination
            var users = await query
                .Skip((filter.PageNumber - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            // Map to DTOs
            var userDtos = new List<UserDto>();
            foreach (var user in users)
            {
                var userDto = _mapper.Map<UserDto>(user);

                // Include role and permission data for admin users
                var roleCodes = user.UserRoles?.Select(ur => ur.Role.Code).ToList() ?? new List<string>();
                userDto.Roles = roleCodes;

                var permissionCodes = user.UserRoles?
                    .SelectMany(ur => ur.Role.RolePermissions ?? new List<RolePermission>())
                    .Select(rp => rp.Permission.Code)
                    .Distinct()
                    .ToList() ?? new List<string>();
                userDto.Permissions = permissionCodes;

                // Get role objects (new detailed objects)
                var roles = user.UserRoles?.Select(ur => ur.Role).ToList() ?? new List<AdminRole>();
                userDto.RoleObjects = _mapper.Map<IEnumerable<AdminRoleDto>>(roles);

                // Get permission objects (new detailed objects)
                var permissions = user.UserRoles?
                    .SelectMany(ur => ur.Role.RolePermissions ?? new List<RolePermission>())
                    .Select(rp => rp.Permission)
                    .Distinct()
                    .ToList() ?? new List<Permission>();
                userDto.PermissionObjects = _mapper.Map<IEnumerable<PermissionDto>>(permissions);

                userDtos.Add(userDto);
            }

            var pageCount = (int)Math.Ceiling(totalCount / (double)filter.PageSize);

            return new PagedResultDto<UserDto>
            {
                Items = userDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = filter.PageNumber,
                PageSize = filter.PageSize
            };
        }

        public async Task<bool> UpdateUserStatusAsync(Guid userId, bool isActive)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            user.IsActive = isActive;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<UserDto> CreateAdminUserAsync(CreateAdminUserDto createAdminUserDto)
        {
            // Validate input
            if (createAdminUserDto.RoleIds == null || !createAdminUserDto.RoleIds.Any())
                throw new InvalidOperationException("At least one role must be assigned to admin user");

            // Check if email already exists
            if (await _unitOfWork.AppUsers.EmailExistsAsync(createAdminUserDto.Email))
                throw new InvalidOperationException("Email already exists");

            // Validate all roles exist before creating user
            foreach (var roleId in createAdminUserDto.RoleIds)
            {
                var roleExists = await _unitOfWork.AdminRoles.GetByIdAsync(roleId);
                if (roleExists == null)
                    throw new InvalidOperationException($"Role with ID {roleId} not found");
            }

            // Generate salt and hash password
            var salt = GenerateRandomSalt();
            var hashedPassword = HashPassword(createAdminUserDto.Password, salt);

            var user = new AppUser
            {
                Email = createAdminUserDto.Email.ToLower(),
                FullName = createAdminUserDto.FullName,
                PasswordHash = hashedPassword,
                PasswordSalt = salt,
                Phone = createAdminUserDto.Phone,
                UserType = EnumValues.UserType.Admin.ToString(),
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            await _unitOfWork.AppUsers.AddAsync(user);

            // Create default notification preferences for the admin user
            var notificationPreferences = new NotificationPreference
            {
                UserId = user.Id,
                ReceivePromotions = true,
                ReceiveWalletUpdates = true,
                ReceiveNews = true,
                ReceiveCustomerMessages = true,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.NotificationPreferences.AddAsync(notificationPreferences);

            // Add roles to the user (roles already validated above)
            foreach (var roleId in createAdminUserDto.RoleIds)
            {
                var userRole = new UserRole
                {
                    UserID = user.Id,
                    RoleID = roleId
                    // Don't set navigation properties (User, Role) to avoid EF trying to insert them
                };
                await _unitOfWork.UserRoles.AddAsync(userRole);
            }

            // Save all changes in a single transaction - if any operation fails, everything will be rolled back
            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<UserDto>(user);
        }

        public async Task<bool> UpdateUserRolesAsync(UpdateUserRoleDto updateUserRoleDto)
        {
            // Validate user exists
            var userExists = await _unitOfWork.AppUsers.GetByIdAsync(updateUserRoleDto.UserId);
            if (userExists == null) return false;

            // Validate all roles exist before making changes
            foreach (var roleId in updateUserRoleDto.RoleIds)
            {
                var roleExists = await _unitOfWork.AdminRoles.GetByIdAsync(roleId);
                if (roleExists == null)
                    throw new InvalidOperationException($"Role with ID {roleId} not found");
            }

            // Remove existing roles
            var existingRoles = await _unitOfWork.UserRoles.GetQueryable()
                .Where(ur => ur.UserID == updateUserRoleDto.UserId)
                .ToListAsync();

            foreach (var role in existingRoles)
            {
                _unitOfWork.UserRoles.Remove(role);
            }

            // Add new roles
            foreach (var roleId in updateUserRoleDto.RoleIds)
            {
                var userRole = new UserRole
                {
                    UserID = updateUserRoleDto.UserId,
                    RoleID = roleId
                    // Don't set navigation properties to avoid EF trying to insert them
                };
                await _unitOfWork.UserRoles.AddAsync(userRole);
            }

            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Private Helper Methods

        private static string GenerateRandomSalt()
        {
            byte[] salt = new byte[128 / 8];
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }
            return Convert.ToBase64String(salt);
        }

        private static string HashPassword(string password, string salt)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var saltedPassword = string.Concat(password, salt);
                var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(saltedPassword));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        private IQueryable<AppUser> ApplyUserFilters(IQueryable<AppUser> query, UserFilterDto filter)
        {
            if (!string.IsNullOrEmpty(filter.Email))
                query = query.Where(u => u.Email.ToLower().Contains(filter.Email.ToLower()));

            if (!string.IsNullOrEmpty(filter.Name))
                query = query.Where(u => u.FullName.ToLower().Contains(filter.Name.ToLower()));

            if (!string.IsNullOrEmpty(filter.Phone))
                query = query.Where(u => u.Phone.Contains(filter.Phone));

            if (!string.IsNullOrEmpty(filter.UserType))
                query = query.Where(u => u.UserType == filter.UserType);

            if (filter.IsActive.HasValue)
                query = query.Where(u => u.IsActive == filter.IsActive.Value);

            return query;
        }

        private IQueryable<AppUser> ApplyUserSorting(IQueryable<AppUser> query, UserFilterDto filter)
        {
            if (string.IsNullOrEmpty(filter.SortColumn))
            {
                return query.OrderByDescending(u => u.CreatedAt);
            }

            switch (filter.SortColumn.ToLower())
            {
                case "fullname":
                case "name":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.FullName)
                        : query.OrderBy(u => u.FullName);
                case "email":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.Email)
                        : query.OrderBy(u => u.Email);
                case "phone":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.Phone)
                        : query.OrderBy(u => u.Phone);
                case "createdat":
                case "created":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.CreatedAt)
                        : query.OrderBy(u => u.CreatedAt);
                case "lastlogin":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.LastLogin)
                        : query.OrderBy(u => u.LastLogin);
                default:
                    return query.OrderByDescending(u => u.CreatedAt);
            }
        }

        #endregion
    }
}

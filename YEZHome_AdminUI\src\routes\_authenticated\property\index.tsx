import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState, useMemo, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import propertyService from '@/services/property-service'
import type { PropertyDto, PropertySearchParams } from '@/lib/types/property'
import { PropertyStatus, PropertyType, PostType, statusMap, propertyTypeMap, postTypeMap, statusColorMap } from '@/lib/enum'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Spinner } from '@/components/ui/spinner'
import { Form } from '@/components/ui/form'
import { useForm } from 'react-hook-form'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ChevronDown, Filter, MoreHorizontal, Eye, Edit, Trash2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { z } from 'zod'

// Define search schema for URL parameters
const propertySearchSchema = z.object({
  page: z.number().optional().default(1),
  pageSize: z.number().optional().default(10),
  postType: z.array(z.string()).optional().default([]),
  propertyType: z.array(z.string()).optional().default([]),
  status: z.array(z.string()).optional().default([]),
})

export const Route = createFileRoute('/_authenticated/property/')({
  validateSearch: propertySearchSchema,
  component: Properties,
  beforeLoad: () => {
    return {
      getTitle: () => 'Danh sách bất động sản'
    };
  }
})

function Properties() {
  const navigate = useNavigate()
  const search = Route.useSearch()

  // Convert search params to the format expected by the API
  const searchParams: PropertySearchParams = {
    page: search.page,
    pageSize: search.pageSize,
    postType: search.postType,
    propertyType: search.propertyType,
    status: search.status,
  }

  // Popover open state
  const [popoverOpen, setPopoverOpen] = useState(false);

  // Fetch property count stats
  const { data: countStats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['propertyCountStats'],
    queryFn: () => propertyService.getPropertyCountByStatus(),
  })

  // Fetch properties based on search params
  const { data: propertiesData, isLoading: isLoadingProperties } = useQuery({
    queryKey: ['properties', searchParams],
    queryFn: () => propertyService.searchProperties(searchParams),
  })

  // Form for filters
  const form = useForm<PropertySearchParams>({
    defaultValues: {
      postType: search.postType,
      propertyType: search.propertyType,
      status: search.status,
    },
  })

  // Update form when search params change (e.g., when navigating back)
  useEffect(() => {
    form.reset({
      postType: search.postType,
      propertyType: search.propertyType,
      status: search.status,
    })
  }, [search, form])

  // Handle filter submission
  const onSubmitFilters = (data: PropertySearchParams) => {
    navigate({
      to: '/property',
      search: {
        ...search,
        ...data,
        page: 1, // Reset to first page on new search
      }
    })
    setPopoverOpen(false); // Close popover after submission
  }

  // Row action handlers
  const handleViewDetails = (id: string) => {
    navigate({
      to: '/property/$propertyId',
      params: { propertyId: id },
      search: {
        from: 'property',
        filters: JSON.stringify(searchParams)
      }
    })
  };

  const handleEdit = (id: string) => {
    navigate({
      to: '/property/$propertyId',
      params: { propertyId: id },
      search: {
        from: 'property',
        filters: JSON.stringify(searchParams),
        mode: 'edit'
      }
    })
  };

  const handleDelete = (id: string) => {
    console.log('Delete property:', id);
    // Implement delete functionality
  };

  // Define table columns
  const columnHelper = createColumnHelper<PropertyDto>()
  const columns = useMemo(() => [
    columnHelper.accessor('name', {
      header: 'Tên',
      cell: info => info.getValue(),
    }),
    columnHelper.accessor('propertyType', {
      header: 'Loại BĐS',
      cell: info => {
        const value = info.getValue();
        return propertyTypeMap[value as PropertyType] || value || '-';
      },
    }),
    columnHelper.accessor('postType', {
      header: 'Loại tin',
      cell: info => {
        const value = info.getValue();
        return postTypeMap[value as PostType] || value || '-';
      },
    }),
    columnHelper.accessor('address', {
      header: 'Địa chỉ',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('price', {
      header: () => <div className="text-right">Giá</div>,
      cell: info => {
        const price = info.getValue()
        return (
          <div className="text-right">
            {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: 'VND',
            }).format(price)}
          </div>
        );
      },
    }),
    columnHelper.accessor('status', {
      header: 'Trạng thái',
      cell: info => {
        const value = info.getValue() as PropertyStatus;
        const variant = getStatusVariant(value);
        return (
          <div className="w-24">
            <Badge
              variant={variant}
              className="whitespace-nowrap overflow-hidden text-ellipsis max-w-full"
              title={statusMap[value] || value || '-'}
            >
              {statusMap[value] || value || '-'}
            </Badge>
          </div>
        );
      },
    }),
    columnHelper.accessor('createdAt', {
      header: 'Ngày tạo',
      cell: info => new Date(info.getValue()).toLocaleDateString('vi-VN'),
    }),
    columnHelper.display({
      id: 'actions',
      header: '',
      cell: info => {
        const id = info.row.original.id;
        return (
          <div className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleViewDetails(id)}>
                  <Eye className="mr-2 h-4 w-4" />
{/* 
                  <Link
                    className="text-blue-500 hover:text-blue-700"
                    to="/property/$propertyId"
                    params={{ propertyId: id }}
                    search={{
                      from: 'property',
                      filters: JSON.stringify(searchParams),
                      mode: 'view'
                    }}
                  >
                    Xem chi tiết
                  </Link> */}
                  <span>Xem chi tiết</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEdit(id)}>
                  <Edit className="mr-2 h-4 w-4" />
                  <span>Chỉnh sửa</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDelete(id)} className="text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Xóa</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    }),
  ], [columnHelper, searchParams])

  // Initialize table
  const table = useReactTable({
    data: propertiesData?.items || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    pageCount: propertiesData?.pageCount || -1,
    manualPagination: true,
    state: {
      pagination: {
        pageIndex: (searchParams.page || 1) - 1,
        pageSize: searchParams.pageSize || 10,
      },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const newPagination = updater(table.getState().pagination)
        navigate({
          to: '/property',
          search: {
            ...search,
            page: newPagination.pageIndex + 1,
            pageSize: newPagination.pageSize,
          }
        })
      } else {
        navigate({
          to: '/property',
          search: {
            ...search,
            page: updater.pageIndex + 1,
            pageSize: updater.pageSize,
          }
        })
      }
    },
  })

  // Status options based on PropertyStatus enum
  const statusOptions = Object.entries(PropertyStatus).map(([_, value]) => ({
    value,
    label: statusMap[value as PropertyStatus] || value,
  }))

  // Property type options based on PropertyType enum
  const propertyTypeOptions = Object.entries(PropertyType).map(([_, value]) => ({
    value,
    label: propertyTypeMap[value as PropertyType] || value,
  }))

  // Post type options based on PostType enum
  const postTypeOptions = Object.entries(PostType).map(([_, value]) => ({
    value,
    label: postTypeMap[value as PostType] || value,
  }))

  // Helper function to determine badge variant based on status
  const getStatusVariant = (status: PropertyStatus | string): "default" | "secondary" | "destructive" | "outline" | "success" | "warning" | "info" | null | undefined => {
    switch (status) {
      case PropertyStatus.Draft:
        return "secondary";
      case PropertyStatus.PendingApproval:
        return "info";
      case PropertyStatus.Approved:
        return "success";
      case PropertyStatus.RejectedByAdmin:
      case PropertyStatus.RejectedDueToUnpaid:
        return "destructive";
      case PropertyStatus.WaitingPayment:
        return "warning";
      case PropertyStatus.Expired:
        return "outline";
      case PropertyStatus.Sold:
        return "default";
      default:
        return "default";
    }
  };

  // Count selected filters
  const selectedFiltersCount =
    (form.watch('propertyType')?.length || 0) +
    (form.watch('postType')?.length || 0) +
    (form.watch('status')?.length || 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Quản lý bất động sản</h1>

        {/* Filter Button with Popover */}
        <div className="flex items-center gap-2">
          <Form {...form}>
            <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Filter className="h-4 w-4" />
                  Bộ lọc
                  {selectedFiltersCount > 0 && (
                    <Badge variant="secondary" className="ml-1 px-1 py-0">
                      {selectedFiltersCount}
                    </Badge>
                  )}
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="end">
                <form onSubmit={form.handleSubmit(onSubmitFilters)} className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Loại BĐS</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {propertyTypeOptions.map(option => (
                        <label key={option.value} className="flex items-center gap-1.5">
                          <input
                            type="checkbox"
                            value={option.value}
                            {...form.register('propertyType')}
                            className="h-4 w-4"
                          />
                          <span className="text-sm">{option.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Loại tin</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {postTypeOptions.map(option => (
                        <label key={option.value} className="flex items-center gap-1.5">
                          <input
                            type="checkbox"
                            value={option.value}
                            {...form.register('postType')}
                            className="h-4 w-4"
                          />
                          <span className="text-sm">{option.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Trạng thái</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {statusOptions.map(option => (
                        <label key={option.value} className="flex items-center gap-1.5">
                          <input
                            type="checkbox"
                            value={option.value}
                            {...form.register('status')}
                            className="h-4 w-4"
                          />
                          <span className="text-sm">{option.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <Button type="submit" className="w-full">
                    Áp dụng bộ lọc
                  </Button>
                </form>
              </PopoverContent>
            </Popover>
          </Form>
        </div>
      </div>

      {/* Status count cards */}
      {isLoadingStats ? (
        <div className="flex justify-center">
          <Spinner />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-9 gap-4">
          <Card className="py-3">
            <CardContent className="p-4">
              <div className="text-center">
                <div className={`text-2xl font-bold ${statusColorMap[PropertyStatus.Draft].split(` `)[1]}`}>{countStats?.totalProperties || 0}</div>
                <div className={`text-xs font-medium mt-1 ${statusColorMap[PropertyStatus.Draft].split(` `)[1]} opacity-80`}>Tổng số</div>
              </div>
            </CardContent>
          </Card>

          {countStats?.propertiesByStatus && Object.entries(countStats.propertiesByStatus).map(([status, count]) => (
            <Card key={status} className={`py-3 ${statusColorMap[status as PropertyStatus]}`}>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className={`text-2xl font-bold ${statusColorMap[status as PropertyStatus].split(` `)[1]}`}>{count}</div>
                  <div className={`text-xs font-medium mt-1 ${statusColorMap[status as PropertyStatus].split(` `)[1]} opacity-80`}>{statusMap[status as PropertyStatus] || status}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Table section */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách bất động sản</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingProperties ? (
            <div className="flex justify-center py-8">
              <Spinner />
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    {table.getHeaderGroups().map(headerGroup => (
                      <tr key={headerGroup.id} className="border-b bg-muted">
                        {headerGroup.headers.map(header => (
                          <th key={header.id} className="px-4 py-3 text-left text-sm font-medium">
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                  <tbody>
                    {table.getRowModel().rows.length ? (
                      table.getRowModel().rows.map(row => (
                        <tr key={row.id} className="border-b hover:bg-muted/50">
                          {row.getVisibleCells().map(cell => (
                            <td key={cell.id} className="px-4 py-3 text-sm">
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </td>
                          ))}
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={columns.length} className="py-6 text-center">
                          Không có dữ liệu
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between py-4">
                <div className="text-sm text-muted-foreground">
                  Hiển thị {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} đến{' '}
                  {Math.min(
                    (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                    propertiesData?.totalCount || 0
                  )}{' '}
                  trong tổng số {propertiesData?.totalCount || 0} kết quả
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                  >
                    Trước
                  </Button>
                  <span className="text-sm">
                    Trang {table.getState().pagination.pageIndex + 1} / {propertiesData?.pageCount || 1}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                  >
                    Sau
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
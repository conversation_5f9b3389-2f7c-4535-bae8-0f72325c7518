export interface UserDto {
  id: string;
  fullName?: string;
  email?: string;
  userType?: string;
  token?: string;
  phone?: string;
  phone2?: string;
  phone3?: string;
  lastLogin?: string;
  totalSpent?: number;
  memberRank?: string;
  avatarImage?: string;
  avatarURL?: string;
  transferCode?: string;
  isActive: boolean;
  personalTaxCode?: string;
  invoiceInfo?: UserInvoiceInfoDto;
  wallet?: WalletDto;
  roles?: string[];
  permissions?: string[];
  roleObjects?: AdminRoleDto[];
  permissionObjects?: PermissionDto[];
}

export interface UserInvoiceInfoDto {
  buyerName?: string;
  email?: string;
  companyName?: string;
  taxCode?: string;
  address?: string;
}

export interface WalletDto {
  id: string;
  userId: string;
  balance: number;
}

export interface AdminRoleDto {
  id: string;
  roleName: string;
  code: string;
}

export interface PermissionDto {
  id: string;
  code: string;
  permissionName: string;
  description?: string;
}

export interface UserDtoPagedResultDto {
  items?: UserDto[];
  totalCount: number;
  pageCount: number;
  currentPage: number;
  pageSize: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export interface CreateAdminUserDto {
  fullName: string;
  email: string;
  password: string;
  phone: string;
  roleIds: string[];
}

export interface UpdateUserStatusDto {
  isActive: boolean;
} 
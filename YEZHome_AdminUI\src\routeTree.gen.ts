/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AuthenticatedDashboardRouteImport } from './routes/_authenticated/dashboard'
import { Route as AuthenticatedPropertyIndexRouteImport } from './routes/_authenticated/property/index'
import { Route as AuthenticatedEmployeeIndexRouteImport } from './routes/_authenticated/employee/index'
import { Route as AuthenticatedCustomerIndexRouteImport } from './routes/_authenticated/customer/index'
import { Route as AuthenticatedPropertyPropertyIdRouteImport } from './routes/_authenticated/property/$propertyId'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedDashboardRoute = AuthenticatedDashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedPropertyIndexRoute =
  AuthenticatedPropertyIndexRouteImport.update({
    id: '/property/',
    path: '/property/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedEmployeeIndexRoute =
  AuthenticatedEmployeeIndexRouteImport.update({
    id: '/employee/',
    path: '/employee/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedCustomerIndexRoute =
  AuthenticatedCustomerIndexRouteImport.update({
    id: '/customer/',
    path: '/customer/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedPropertyPropertyIdRoute =
  AuthenticatedPropertyPropertyIdRouteImport.update({
    id: '/property/$propertyId',
    path: '/property/$propertyId',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/property/$propertyId': typeof AuthenticatedPropertyPropertyIdRoute
  '/customer': typeof AuthenticatedCustomerIndexRoute
  '/employee': typeof AuthenticatedEmployeeIndexRoute
  '/property': typeof AuthenticatedPropertyIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/property/$propertyId': typeof AuthenticatedPropertyPropertyIdRoute
  '/customer': typeof AuthenticatedCustomerIndexRoute
  '/employee': typeof AuthenticatedEmployeeIndexRoute
  '/property': typeof AuthenticatedPropertyIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/dashboard': typeof AuthenticatedDashboardRoute
  '/_authenticated/property/$propertyId': typeof AuthenticatedPropertyPropertyIdRoute
  '/_authenticated/customer/': typeof AuthenticatedCustomerIndexRoute
  '/_authenticated/employee/': typeof AuthenticatedEmployeeIndexRoute
  '/_authenticated/property/': typeof AuthenticatedPropertyIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/login'
    | '/dashboard'
    | '/property/$propertyId'
    | '/customer'
    | '/employee'
    | '/property'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/login'
    | '/dashboard'
    | '/property/$propertyId'
    | '/customer'
    | '/employee'
    | '/property'
  id:
    | '__root__'
    | '/'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/dashboard'
    | '/_authenticated/property/$propertyId'
    | '/_authenticated/customer/'
    | '/_authenticated/employee/'
    | '/_authenticated/property/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/dashboard': {
      id: '/_authenticated/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthenticatedDashboardRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/property/': {
      id: '/_authenticated/property/'
      path: '/property'
      fullPath: '/property'
      preLoaderRoute: typeof AuthenticatedPropertyIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/employee/': {
      id: '/_authenticated/employee/'
      path: '/employee'
      fullPath: '/employee'
      preLoaderRoute: typeof AuthenticatedEmployeeIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/customer/': {
      id: '/_authenticated/customer/'
      path: '/customer'
      fullPath: '/customer'
      preLoaderRoute: typeof AuthenticatedCustomerIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/property/$propertyId': {
      id: '/_authenticated/property/$propertyId'
      path: '/property/$propertyId'
      fullPath: '/property/$propertyId'
      preLoaderRoute: typeof AuthenticatedPropertyPropertyIdRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedDashboardRoute: typeof AuthenticatedDashboardRoute
  AuthenticatedPropertyPropertyIdRoute: typeof AuthenticatedPropertyPropertyIdRoute
  AuthenticatedCustomerIndexRoute: typeof AuthenticatedCustomerIndexRoute
  AuthenticatedEmployeeIndexRoute: typeof AuthenticatedEmployeeIndexRoute
  AuthenticatedPropertyIndexRoute: typeof AuthenticatedPropertyIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedDashboardRoute: AuthenticatedDashboardRoute,
  AuthenticatedPropertyPropertyIdRoute: AuthenticatedPropertyPropertyIdRoute,
  AuthenticatedCustomerIndexRoute: AuthenticatedCustomerIndexRoute,
  AuthenticatedEmployeeIndexRoute: AuthenticatedEmployeeIndexRoute,
  AuthenticatedPropertyIndexRoute: AuthenticatedPropertyIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

import axiosInstance from './axios-config';
import type { UserDto, UserDtoPagedResultDto, CreateAdminUserDto, UpdateUserStatusDto } from '@/lib/types/user';

interface EmployeeSearchParams {
  PageNumber?: number;
  PageSize?: number;
  Email?: string;
  Name?: string;
  Phone?: string;
  UserType?: string;
  IsActive?: boolean;
  SortColumn?: string;
  SortDescending?: boolean;
}

const userService = {
  // Lấy danh sách nhân viên với bộ lọc
  getEmployees: async (params: EmployeeSearchParams): Promise<UserDtoPagedResultDto> => {
    const response = await axiosInstance.get('/User/employees', { params });
    return response.data;
  },

  // Lấy thông tin chi tiết của nhân viên
  getUserById: async (id: string): Promise<UserDto> => {
    const response = await axiosInstance.get(`/User/${id}`);
    return response.data;
  },

  // Thêm nhân viên mới
  createEmployee: async (data: CreateAdminUserDto): Promise<UserDto> => {
    const response = await axiosInstance.post('/User/employees', data);
    return response.data;
  },

  // Cập nhật trạng thái nhân viên (kích hoạt/vô hiệu hóa)
  updateUserStatus: async (userId: string, data: UpdateUserStatusDto): Promise<void> => {
    await axiosInstance.put(`/User/${userId}/status`, data);
  },

  // Xóa nhân viên (giả định API endpoint)
  deleteUser: async (userId: string): Promise<void> => {
    // Note: This is a placeholder. The actual API endpoint for deletion might be different
    // or might not exist in the current API documentation
    await axiosInstance.delete(`/User/${userId}`);
  }
};

export default userService; 
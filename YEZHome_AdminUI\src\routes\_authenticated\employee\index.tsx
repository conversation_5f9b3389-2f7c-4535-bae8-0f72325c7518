import { createFile<PERSON>oute, useNavigate } from '@tanstack/react-router'
import { useState, useMemo, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import userService from '@/services/user-service'
import type { UserDto } from '@/lib/types/user'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Spinner } from '@/components/ui/spinner'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { Form } from '@/components/ui/form'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { MoreHorizontal, Eye, Edit, Trash2, Plus, Search } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from '@/components/ui/use-toast'

// Define search schema for URL parameters
const employeeSearchSchema = z.object({
  page: z.number().optional().default(1),
  pageSize: z.number().optional().default(10),
  email: z.string().optional().default(''),
  name: z.string().optional().default(''),
  phone: z.string().optional().default(''),
  sortColumn: z.string().optional(),
  sortDescending: z.boolean().optional(),
})

// Define schema for creating a new employee
const createEmployeeSchema = z.object({
  fullName: z.string().min(1, 'Họ tên không được để trống'),
  email: z.string().email('Email không hợp lệ').min(1, 'Email không được để trống'),
  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
  phone: z.string().min(1, 'Số điện thoại không được để trống'),
  roleIds: z.array(z.string()).min(1, 'Phải chọn ít nhất một vai trò'),
})

type EmployeeSearchParams = z.infer<typeof employeeSearchSchema>
type CreateEmployeeFormData = z.infer<typeof createEmployeeSchema>

export const Route = createFileRoute('/_authenticated/employee/')({
  validateSearch: employeeSearchSchema,
  component: Employees,
  beforeLoad: () => {
    return {
      getTitle: () => 'Quản lý nhân viên'
    };
  }
})

function Employees() {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const search = Route.useSearch()

  // State for dialogs
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<UserDto | null>(null)

  // Convert search params to the format expected by the API
  const searchParams = useMemo(() => ({
    PageNumber: search.page,
    PageSize: search.pageSize,
    Email: search.email || undefined,
    Name: search.name || undefined,
    Phone: search.phone || undefined,
    SortColumn: search.sortColumn,
    SortDescending: search.sortDescending,
  }), [search])

  // Search form
  const searchForm = useForm<EmployeeSearchParams>({
    defaultValues: {
      email: search.email,
      name: search.name,
      phone: search.phone,
    },
  })

  // Create employee form
  const createForm = useForm<CreateEmployeeFormData>({
    resolver: zodResolver(createEmployeeSchema),
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      phone: '',
      roleIds: [],
    },
  })

  // Fetch employees based on search params
  const { data: employeesData, isLoading: isLoadingEmployees } = useQuery({
    queryKey: ['employees', searchParams],
    queryFn: () => userService.getEmployees(searchParams),
  })

  // Fetch employee details
  const { data: employeeDetails, isLoading: isLoadingDetails, refetch: refetchDetails } = useQuery({
    queryKey: ['employee', selectedEmployee?.id],
    queryFn: () => selectedEmployee?.id ? userService.getUserById(selectedEmployee.id) : null,
    enabled: !!selectedEmployee?.id && (isViewDialogOpen || isEditDialogOpen),
  })

  // Mutations
  const createEmployeeMutation = useMutation({
    mutationFn: userService.createEmployee,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      setIsAddDialogOpen(false)
      createForm.reset()
      toast({
        title: "Thành công",
        description: "Đã thêm nhân viên mới",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể thêm nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error creating employee:', error)
    }
  })

  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: string, status: boolean }) => 
      userService.updateUserStatus(id, { isActive: status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      setIsEditDialogOpen(false)
      toast({
        title: "Thành công",
        description: "Đã cập nhật trạng thái nhân viên",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật trạng thái nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error updating employee status:', error)
    }
  })

  const deleteEmployeeMutation = useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      setIsDeleteDialogOpen(false)
      setSelectedEmployee(null)
      toast({
        title: "Thành công",
        description: "Đã xóa nhân viên",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể xóa nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error deleting employee:', error)
    }
  })

  // Update search form when search params change (e.g., when navigating back)
  useEffect(() => {
    searchForm.reset({
      email: search.email,
      name: search.name,
      phone: search.phone,
    })
  }, [search, searchForm])

  // Handle search submission
  const onSubmitSearch = (data: EmployeeSearchParams) => {
    navigate({
      to: '/employee',
      search: {
        ...search,
        ...data,
        page: 1, // Reset to first page on new search
      }
    })
  }

  // Handle create employee submission
  const onSubmitCreateEmployee = (data: CreateEmployeeFormData) => {
    createEmployeeMutation.mutate(data)
  }

  // Row action handlers
  const handleViewDetails = (employee: UserDto) => {
    setSelectedEmployee(employee)
    setIsViewDialogOpen(true)
  }

  const handleEdit = (employee: UserDto) => {
    setSelectedEmployee(employee)
    setIsEditDialogOpen(true)
  }

  const handleDelete = (employee: UserDto) => {
    setSelectedEmployee(employee)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (selectedEmployee) {
      deleteEmployeeMutation.mutate(selectedEmployee.id)
    }
  }

  // Define table columns
  const columnHelper = createColumnHelper<UserDto>()
  const columns = useMemo(() => [
    columnHelper.accessor('fullName', {
      header: 'Họ tên',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('email', {
      header: 'Email',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('phone', {
      header: 'Số điện thoại',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('userType', {
      header: 'Loại người dùng',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('isActive', {
      header: 'Trạng thái',
      cell: info => {
        const isActive = info.getValue()
        return (
          <Badge variant={isActive ? "success" : "destructive"}>
            {isActive ? 'Đang hoạt động' : 'Đã vô hiệu hóa'}
          </Badge>
        )
      },
    }),
    columnHelper.accessor(row => row.lastLogin ? new Date(row.lastLogin).toLocaleDateString('vi-VN') : '-', {
      id: 'lastLogin',
      header: 'Đăng nhập cuối',
      cell: info => info.getValue(),
    }),
    columnHelper.display({
      id: 'actions',
      header: '',
      cell: info => {
        const employee = info.row.original;
        return (
          <div className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleViewDetails(employee)}>
                  <Eye className="mr-2 h-4 w-4" />
                  <span>Xem chi tiết</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEdit(employee)}>
                  <Edit className="mr-2 h-4 w-4" />
                  <span>Chỉnh sửa</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDelete(employee)} className="text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Xóa</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    }),
  ], [])

  // Initialize table
  const table = useReactTable({
    data: employeesData?.items || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    pageCount: employeesData?.pageCount || -1,
    manualPagination: true,
    state: {
      pagination: {
        pageIndex: (searchParams.PageNumber || 1) - 1,
        pageSize: searchParams.PageSize || 10,
      },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const newPagination = updater(table.getState().pagination)
        navigate({
          to: '/employee',
          search: {
            ...search,
            page: newPagination.pageIndex + 1,
            pageSize: newPagination.pageSize,
          }
        })
      } else {
        navigate({
          to: '/employee',
          search: {
            ...search,
            page: updater.pageIndex + 1,
            pageSize: updater.pageSize,
          }
        })
      }
    },
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Quản lý nhân viên</h1>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Thêm nhân viên
        </Button>
      </div>

      {/* Search form */}
      <Card>
        <CardHeader>
          <CardTitle>Tìm kiếm nhân viên</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...searchForm}>
            <form onSubmit={searchForm.handleSubmit(onSubmitSearch)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    placeholder="Nhập email"
                    {...searchForm.register('email')}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Họ tên</Label>
                  <Input
                    id="name"
                    placeholder="Nhập họ tên"
                    {...searchForm.register('name')}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Số điện thoại</Label>
                  <Input
                    id="phone"
                    placeholder="Nhập số điện thoại"
                    {...searchForm.register('phone')}
                  />
                </div>
              </div>
              <div className="flex justify-end">
                <Button type="submit">
                  <Search className="mr-2 h-4 w-4" />
                  Tìm kiếm
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Table section */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách nhân viên</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingEmployees ? (
            <div className="flex justify-center py-8">
              <Spinner />
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    {table.getHeaderGroups().map(headerGroup => (
                      <tr key={headerGroup.id} className="border-b bg-muted">
                        {headerGroup.headers.map(header => (
                          <th key={header.id} className="px-4 py-3 text-left text-sm font-medium">
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                  <tbody>
                    {table.getRowModel().rows.length ? (
                      table.getRowModel().rows.map(row => (
                        <tr key={row.id} className="border-b hover:bg-muted/50">
                          {row.getVisibleCells().map(cell => (
                            <td key={cell.id} className="px-4 py-3 text-sm">
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </td>
                          ))}
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={columns.length} className="py-6 text-center">
                          Không có dữ liệu
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between py-4">
                <div className="text-sm text-muted-foreground">
                  Hiển thị {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} đến{' '}
                  {Math.min(
                    (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                    employeesData?.totalCount || 0
                  )}{' '}
                  trong tổng số {employeesData?.totalCount || 0} kết quả
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                  >
                    Trước
                  </Button>
                  <span className="text-sm">
                    Trang {table.getState().pagination.pageIndex + 1} / {employeesData?.pageCount || 1}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                  >
                    Sau
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Employee Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Thêm nhân viên mới</DialogTitle>
          </DialogHeader>
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(onSubmitCreateEmployee)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="fullName">Họ tên</Label>
                <Input
                  id="fullName"
                  placeholder="Nhập họ tên"
                  {...createForm.register('fullName')}
                />
                {createForm.formState.errors.fullName && (
                  <p className="text-sm text-red-500">{createForm.formState.errors.fullName.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Nhập email"
                  {...createForm.register('email')}
                />
                {createForm.formState.errors.email && (
                  <p className="text-sm text-red-500">{createForm.formState.errors.email.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Mật khẩu</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Nhập mật khẩu"
                  {...createForm.register('password')}
                />
                {createForm.formState.errors.password && (
                  <p className="text-sm text-red-500">{createForm.formState.errors.password.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Số điện thoại</Label>
                <Input
                  id="phone"
                  placeholder="Nhập số điện thoại"
                  {...createForm.register('phone')}
                />
                {createForm.formState.errors.phone && (
                  <p className="text-sm text-red-500">{createForm.formState.errors.phone.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label>Vai trò</Label>
                <div className="grid grid-cols-2 gap-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      value="admin-role-id" // Replace with actual role ID
                      {...createForm.register('roleIds')}
                    />
                    <span>Quản trị viên</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      value="staff-role-id" // Replace with actual role ID
                      {...createForm.register('roleIds')}
                    />
                    <span>Nhân viên</span>
                  </label>
                </div>
                {createForm.formState.errors.roleIds && (
                  <p className="text-sm text-red-500">{createForm.formState.errors.roleIds.message}</p>
                )}
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Thoát
                </Button>
                <Button type="submit" disabled={createEmployeeMutation.isPending}>
                  {createEmployeeMutation.isPending ? (
                    <>
                      <Spinner className="mr-2 h-4 w-4" />
                      Đang thêm...
                    </>
                  ) : (
                    'Thêm'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* View Employee Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Chi tiết nhân viên</DialogTitle>
          </DialogHeader>
          {isLoadingDetails ? (
            <div className="flex justify-center py-8">
              <Spinner />
            </div>
          ) : employeeDetails ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium">Họ tên</h4>
                  <p>{employeeDetails.fullName || '-'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Email</h4>
                  <p>{employeeDetails.email || '-'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Số điện thoại</h4>
                  <p>{employeeDetails.phone || '-'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Loại người dùng</h4>
                  <p>{employeeDetails.userType || '-'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Trạng thái</h4>
                  <Badge variant={employeeDetails.isActive ? "success" : "destructive"}>
                    {employeeDetails.isActive ? 'Đang hoạt động' : 'Đã vô hiệu hóa'}
                  </Badge>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Đăng nhập cuối</h4>
                  <p>{employeeDetails.lastLogin ? new Date(employeeDetails.lastLogin).toLocaleString('vi-VN') : '-'}</p>
                </div>
              </div>
              
              {employeeDetails.roleObjects && employeeDetails.roleObjects.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium">Vai trò</h4>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {employeeDetails.roleObjects.map((role, index) => (
                      <Badge key={index} variant="outline">{role.roleName}</Badge>
                    ))}
                  </div>
                </div>
              )}
              
              <DialogFooter>
                <Button onClick={() => setIsViewDialogOpen(false)}>
                  Đóng
                </Button>
              </DialogFooter>
            </div>
          ) : (
            <p className="text-center py-4">Không tìm thấy thông tin nhân viên</p>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Employee Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Chỉnh sửa nhân viên</DialogTitle>
          </DialogHeader>
          {isLoadingDetails ? (
            <div className="flex justify-center py-8">
              <Spinner />
            </div>
          ) : employeeDetails ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium">Họ tên</h4>
                  <p>{employeeDetails.fullName || '-'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Email</h4>
                  <p>{employeeDetails.email || '-'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Số điện thoại</h4>
                  <p>{employeeDetails.phone || '-'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Loại người dùng</h4>
                  <p>{employeeDetails.userType || '-'}</p>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium mb-2">Trạng thái tài khoản</h4>
                <div className="flex items-center space-x-4">
                  <Button 
                    variant={employeeDetails.isActive ? "outline" : "default"}
                    onClick={() => updateStatusMutation.mutate({ id: employeeDetails.id, status: false })}
                    disabled={!employeeDetails.isActive || updateStatusMutation.isPending}
                  >
                    Vô hiệu hóa
                  </Button>
                  <Button 
                    variant={employeeDetails.isActive ? "default" : "outline"}
                    onClick={() => updateStatusMutation.mutate({ id: employeeDetails.id, status: true })}
                    disabled={employeeDetails.isActive || updateStatusMutation.isPending}
                  >
                    Kích hoạt
                  </Button>
                </div>
              </div>
              
              <DialogFooter>
                <Button onClick={() => setIsEditDialogOpen(false)}>
                  Đóng
                </Button>
              </DialogFooter>
            </div>
          ) : (
            <p className="text-center py-4">Không tìm thấy thông tin nhân viên</p>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa nhân viên</AlertDialogTitle>
            <AlertDialogDescription>
              Hành động này sẽ xóa vĩnh viễn nhân viên và không thể khôi phục. Bạn có chắc chắn muốn tiếp tục?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Thoát</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              {deleteEmployeeMutation.isPending ? (
                <>
                  <Spinner className="mr-2 h-4 w-4" />
                  Đang xóa...
                </>
              ) : (
                'Xác nhận xóa'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

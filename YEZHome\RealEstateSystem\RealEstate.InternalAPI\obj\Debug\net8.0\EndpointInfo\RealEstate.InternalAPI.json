{"openapi": "3.0.1", "info": {"title": "RealEstate.InternalAPI", "version": "1.0"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Blog/{id}": {"get": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}}}}}, "put": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Blog": {"post": {"tags": ["Blog"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}}}}}}, "/api/Notification/by-type/{type}/user/{userId}": {"get": {"tags": ["Notification"], "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Notification": {"post": {"tags": ["Notification"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotificationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateNotificationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateNotificationDto"}}}}, "responses": {"201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NotificationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NotificationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Property/{propertyId}": {"get": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "delete": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/bulk": {"delete": {"tags": ["Property"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkPropertyIdsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkPropertyIdsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkPropertyIdsDto"}}}}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/{propertyId}/status": {"put": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}}}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/bulk/status": {"put": {"tags": ["Property"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkUpdateStatusDto"}}}}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/search": {"get": {"tags": ["Property"], "parameters": [{"name": "postType", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "propertyType", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "status", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Property/count-by-status": {"get": {"tags": ["Property"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyCountStatsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyCountStatsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyCountStatsDto"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Property/search/count": {"get": {"tags": ["Property"], "parameters": [{"name": "postType", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "propertyType", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "status", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Property/{propertyId}/history": {"get": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyStatusLogDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyStatusLogDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyStatusLogDto"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/User/{id}": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User/dashboard/{userId}": {"get": {"tags": ["User"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDashboardDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDashboardDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDashboardDto"}}}}}}}, "/api/User/reactivate/{userId}": {"post": {"tags": ["User"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User/{id}/invoice-info": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}}}}}}, "/api/User/customers": {"get": {"tags": ["User"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Phone", "in": "query", "schema": {"type": "string"}}, {"name": "UserType", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "SortColumn", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResultDto"}}}}}}}, "/api/User/employees": {"get": {"tags": ["User"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Phone", "in": "query", "schema": {"type": "string"}}, {"name": "UserType", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "SortColumn", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResultDto"}}}}}}, "post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAdminUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAdminUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/User/{userId}/status": {"put": {"tags": ["User"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/roles": {"put": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRoleDto"}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AdminRoleDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "roleName": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlogCommentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "commentText": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "postId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "BlogPostDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "authorID": {"type": "string", "format": "uuid"}, "authorName": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "featuredImage": {"type": "string", "nullable": true}, "tags": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "isFeature": {"type": "boolean"}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}, "blogComments": {"type": "array", "items": {"$ref": "#/components/schemas/BlogCommentDto"}, "nullable": true}}, "additionalProperties": false}, "BulkPropertyIdsDto": {"required": ["propertyIds"], "type": "object", "properties": {"propertyIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "additionalProperties": false}, "BulkUpdateStatusDto": {"required": ["propertyIds", "status"], "type": "object", "properties": {"propertyIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "status": {"minLength": 1, "type": "string"}, "comment": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateAdminUserDto": {"required": ["email", "fullName", "password", "phone", "roleIds"], "type": "object", "properties": {"fullName": {"minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 6, "type": "string"}, "phone": {"minLength": 1, "type": "string"}, "roleIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "additionalProperties": false}, "CreateBlogPostDto": {"required": ["authorID", "content", "title"], "type": "object", "properties": {"authorID": {"type": "string", "format": "uuid"}, "title": {"maxLength": 100, "minLength": 0, "type": "string"}, "content": {"minLength": 1, "type": "string"}, "featuredImage": {"type": "string", "nullable": true}, "tags": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CreateNotificationDto": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid", "nullable": true}, "type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "relatedEntityId": {"type": "string", "format": "uuid", "nullable": true}, "relatedPropertyId": {"type": "string", "format": "uuid", "nullable": true}, "actionUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginDto": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "MemberRankingDto": {"type": "object", "properties": {"currentRank": {"type": "string", "nullable": true}, "nextRank": {"type": "string", "nullable": true}, "spendingToNextRank": {"type": "number", "format": "double", "nullable": true}, "minSpent": {"type": "number", "format": "double", "nullable": true}, "maxSpent": {"type": "number", "format": "double", "nullable": true}, "progressPercentage": {"type": "number", "format": "double"}}, "additionalProperties": false}, "NotificationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "isRead": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "relatedEntityId": {"type": "string", "format": "uuid", "nullable": true}, "relatedPropertyId": {"type": "string", "format": "uuid", "nullable": true}, "actionUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NotificationDtoPagedResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PermissionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "permissionName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "PropertyCountStatsDto": {"type": "object", "properties": {"totalProperties": {"type": "integer", "format": "int32"}, "propertiesByStatus": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "PropertyDto": {"required": ["address", "description", "name", "price"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "propertyType": {"type": "string", "nullable": true}, "postType": {"type": "string", "nullable": true}, "cityId": {"type": "integer", "format": "int32", "nullable": true}, "districtId": {"type": "integer", "format": "int32", "nullable": true}, "streetId": {"type": "integer", "format": "int32", "nullable": true}, "wardId": {"type": "integer", "format": "int32", "nullable": true}, "address": {"type": "string", "nullable": true}, "area": {"type": "number", "format": "double", "nullable": true}, "price": {"type": "number", "format": "double"}, "videoUrl": {"type": "string", "nullable": true}, "floors": {"type": "integer", "format": "int32", "nullable": true}, "rooms": {"type": "integer", "format": "int32", "nullable": true}, "toilets": {"type": "integer", "format": "int32", "nullable": true}, "direction": {"type": "string", "nullable": true}, "balconyDirection": {"type": "string", "nullable": true}, "legality": {"type": "string", "nullable": true}, "interior": {"type": "string", "nullable": true}, "width": {"type": "integer", "format": "int32", "nullable": true}, "roadWidth": {"type": "integer", "format": "int32", "nullable": true}, "description": {"type": "string", "nullable": true}, "overview": {"type": "string", "nullable": true}, "placeData": {"type": "string", "nullable": true}, "policies": {"type": "string", "nullable": true}, "neighborhood": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "postPrice": {"type": "number", "format": "double"}, "isHighlighted": {"type": "boolean"}, "isAutoRenew": {"type": "boolean"}, "expiresAt": {"type": "string", "format": "date-time"}, "updateRemainingTimes": {"type": "integer", "format": "int32", "nullable": true}, "propertyMedia": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyMediaDtoResponse"}, "nullable": true}, "ownerId": {"type": "string", "format": "uuid"}, "owner": {"$ref": "#/components/schemas/UserInformationDto"}, "createdAt": {"type": "string", "format": "date-time"}, "swLat": {"type": "number", "format": "double", "nullable": true}, "swLng": {"type": "number", "format": "double", "nullable": true}, "neLat": {"type": "number", "format": "double", "nullable": true}, "neLng": {"type": "number", "format": "double", "nullable": true}, "latitude": {"type": "number", "format": "double", "nullable": true}, "longitude": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "PropertyDtoPagedResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PropertyMediaDtoResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "mediaURL": {"type": "string", "nullable": true}, "caption": {"type": "string", "nullable": true}, "isAvatar": {"type": "boolean"}}, "additionalProperties": false}, "PropertyStatsDto": {"type": "object", "properties": {"totalProperties": {"type": "integer", "format": "int32"}, "activeProperties": {"type": "integer", "format": "int32"}, "expiredProperties": {"type": "integer", "format": "int32"}, "draftProperties": {"type": "integer", "format": "int32"}, "favoriteProperties": {"type": "integer", "format": "int32"}, "totalViews": {"type": "integer", "format": "int32"}, "averageRating": {"type": "number", "format": "double"}}, "additionalProperties": false}, "PropertyStatusLogDto": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "changedAt": {"type": "string", "format": "date-time"}, "comment": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateStatusDto": {"required": ["status"], "type": "object", "properties": {"status": {"minLength": 1, "type": "string"}, "comment": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserRoleDto": {"required": ["roleIds", "userId"], "type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "roleIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "additionalProperties": false}, "UpdateUserStatusDto": {"required": ["isActive"], "type": "object", "properties": {"isActive": {"type": "boolean"}}, "additionalProperties": false}, "UserDashboardDto": {"type": "object", "properties": {"userInfo": {"$ref": "#/components/schemas/UserInfoDto"}, "walletInfo": {"$ref": "#/components/schemas/WalletInfoDto"}, "propertyStats": {"$ref": "#/components/schemas/PropertyStatsDto"}, "recentTransactions": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}, "nullable": true}, "memberRanking": {"$ref": "#/components/schemas/MemberRankingDto"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "userType": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "phone2": {"type": "string", "nullable": true}, "phone3": {"type": "string", "nullable": true}, "lastLogin": {"type": "string", "format": "date-time", "nullable": true}, "totalSpent": {"type": "number", "format": "double", "nullable": true}, "memberRank": {"type": "string", "nullable": true}, "avatarImage": {"type": "string", "nullable": true}, "avatarURL": {"type": "string", "nullable": true}, "transferCode": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "personalTaxCode": {"type": "string", "nullable": true}, "invoiceInfo": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}, "wallet": {"$ref": "#/components/schemas/WalletDto"}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "permissions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "roleObjects": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRoleDto"}, "nullable": true}, "permissionObjects": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionDto"}, "nullable": true}}, "additionalProperties": false}, "UserDtoPagedResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "UserInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "userType": {"type": "string", "nullable": true}, "memberRank": {"type": "string", "nullable": true}, "lastLogin": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserInformationDto": {"type": "object", "properties": {"fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "memberRank": {"type": "string", "nullable": true}, "avatarURL": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserInvoiceInfoDto": {"type": "object", "properties": {"buyerName": {"type": "string", "nullable": true}, "email": {"type": "string", "format": "email", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "taxCode": {"pattern": "^(\\d{10}|\\d{10}-\\d{3})$", "type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WalletDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "balance": {"type": "number", "format": "double"}}, "additionalProperties": false}, "WalletInfoDto": {"type": "object", "properties": {"balance": {"type": "number", "format": "double"}, "totalSpent": {"type": "number", "format": "double"}, "totalTransactions": {"type": "integer", "format": "int32"}, "lastMonthSpending": {"type": "number", "format": "double"}}, "additionalProperties": false}, "WalletTransactionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "amount": {"type": "number", "format": "double"}, "type": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "paymentMethod": {"type": "string", "nullable": true}, "transactionReference": {"type": "string", "nullable": true}, "processedAt": {"type": "string", "format": "date-time", "nullable": true}, "failureReason": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}
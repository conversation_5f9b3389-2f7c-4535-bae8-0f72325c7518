﻿using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class ProfileDto
    {
        public UserDto User { get; set; }
        public MemberRankListDto MemberRankDetails { get; set; }
        public HighlightFeeDto HighlightFee { get; set; }
    }

    public class UserDto
    {
        public Guid Id { get; set; }
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? UserType { get; set; }
        public string? Token { get; set; }
        public string? Phone { get; set; }
        public string? Phone2 { get; set; }
        public string? Phone3 { get; set; }

        public DateTime? LastLogin { get; set; }
        public decimal? TotalSpent { get; set; }
        public string? MemberRank { get; set; }
        public string? AvatarImage { get; set; }
        public string? AvatarURL { get; set; }
        public string? TransferCode { get; set; }
        public bool IsActive { get; set; } = true;
        public string? PersonalTaxCode { get; set; }
        public UserInvoiceInfoDto? InvoiceInfo { get; set; }
        public WalletDto? Wallet { get; set; }

        // Admin-specific properties (only codes for frontend)
        public IEnumerable<string>? Roles { get; set; }
        public IEnumerable<string>? Permissions { get; set; }

        // Admin-specific properties (detailed objects)
        public IEnumerable<AdminRoleDto>? RoleObjects { get; set; }
        public IEnumerable<PermissionDto>? PermissionObjects { get; set; }
    }

    public class UserInformationDto
    {
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? MemberRank { get; set; }
        public string? AvatarURL { get; set; }
    }

    public class LoginDto
    {
        [Required]
        public string Email { get; set; }
        [Required]
        public string Password { get; set; }
    }

    public class ForgotPasswordDto
    {

        [Required]
        public string Email { get; set; }

    }
}
